{"name": "on-target-analysis-for-ynab", "version": "0.1.0", "private": true, "description": "On Target Analysis for YNAB - Budget target alignment dashboard", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "deploy:gcp": "./scripts/deploy-gcp.sh", "deploy:secrets": "./scripts/setup-secrets.sh", "deploy:check": "./scripts/deploy-gcp.sh check", "deploy:build": "./scripts/deploy-gcp.sh build"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.0", "axios": "^1.11.0", "date-fns": "^3.6.0", "lodash": "^4.17.21", "next": "^15.4.6", "next-auth": "^4.24.11", "postcss": "^8.5.6", "posthog-js": "^1.259.0", "posthog-node": "^5.6.0", "react": "^18.3.0", "react-dom": "^18.3.0", "recharts": "^2.15.4", "swr": "^2.3.5", "tailwindcss": "^3.4.0", "typescript": "^5.9.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.1.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "overrides": {"test-exclude": "^7.0.0"}}